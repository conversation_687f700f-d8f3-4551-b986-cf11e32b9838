@extends('admin.layouts.app')

@section('title', 'Siswa')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Siswa',
        'breadcrumb' => 'Manajemen Akun',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar @yield('title')
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-students">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="export-btn">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-success" id="import-btn">
                                <i class="ri-upload-line align-bottom"></i> Import
                            </button>
                            <a href="{{ route('admin.students.create') }}" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <!-- Status Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-status" class="form-label">Status</label>
                                <select class="form-select" data-choices name="status" id="filter-status">
                                    <option value="">Semua Status</option>
                                    @foreach ($statuses as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Gender Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-gender" class="form-label">Jenis Kelamin</label>
                                <select class="form-select" data-choices name="gender" id="filter-gender">
                                    <option value="">Semua</option>
                                    @foreach ($genders as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Classroom Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-classroom" class="form-label">Kelas</label>
                                <select class="form-select" data-choices id="filter-classroom" name="classroom">
                                    <option value="">Semua Kelas</option>
                                    @foreach ($classrooms as $classroom)
                                        <option value="{{ $classroom->id }}">{{ $classroom->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Search Input -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari nama, NIS, NISN..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="students-table" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>NISN</th>
                                    <th>Nama Lengkap</th>
                                    <th>Email</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Nomor Telp</th>
                                    <th>Kelas</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="list">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>




@endsection

@push('styles')
    <style type="text/css">
        .dataTables_length,
        .dataTables_filter {
            display: none !important;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.datatables')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var dataTable = $('#students-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.students.index') }}",
                    data: function(d) {
                        d.status = $('#filter-status').val();
                        d.gender = $('#filter-gender').val();
                        d.classroom_id = $('#filter-classroom').val();
                        d.search = $('#search-input').val();
                    },
                    complete: function(response) {
                        $('#total-students').text(response.responseJSON.recordsTotal || 0);
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Error!',
                            text: xhr.responseJSON?.message || 'Gagal memuat data',
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    }, {
                        data: 'nisn',
                        name: 'nisn'
                    },
                    {
                        data: 'user.name',
                        name: 'user.name'
                    },
                    {
                        data: 'user.email',
                        name: 'user.email'
                    },
                    {
                        data: 'gender',
                        name: 'gender'
                    }, {
                        data: 'user.phone_number',
                        name: 'user.phone_number'
                    },
                    {
                        data: 'classrooms',
                        name: 'classrooms',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [3, 'asc']
                ], // Order by name
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    }
                }
            });

            // Adjust table columns on sidebar toggle
            $('#topnav-hamburger-icon').click(function() {
                setTimeout(() => {
                    dataTable.columns.adjust().draw();
                }, 300);
            });

            // Filter by status, gender, or classroom
            $('#filter-status, #filter-gender, #filter-classroom').change(function() {
                dataTable.draw();
            });

            // Search on Enter key
            $('#search-input').keyup(function(e) {
                if (e.key === 'Enter') {
                    dataTable.draw();
                }
            });

            // Search button click
            $('#search-button').click(function() {
                dataTable.draw();
            });

            // Export button click
            $('#export-btn').click(function() {
                Swal.fire({
                    title: 'Coming Soon!',
                    text: 'Fitur export akan segera tersedia.',
                    icon: 'info'
                });
            });

            // Import button click
            $('#import-btn').click(function() {
                Swal.fire({
                    title: 'Coming Soon!',
                    text: 'Fitur import akan segera tersedia.',
                    icon: 'info'
                });
            });

            // Handle delete button clicks with event delegation
            $('#students-table').on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                if (!url) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'URL hapus tidak ditemukan',
                        icon: 'error'
                    });
                    return;
                }

                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus data siswa ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire({
                                        title: 'Berhasil!',
                                        text: response.message,
                                        icon: 'success',
                                        showConfirmButton: false,
                                        timer: 1500
                                    });
                                    dataTable.ajax.reload();
                                } else {
                                    Swal.fire({
                                        title: 'Gagal!',
                                        text: response.message,
                                        icon: 'error'
                                    });
                                }
                            },
                            error: function(xhr) {
                                let message = 'Terjadi kesalahan saat menghapus data';
                                if (xhr.responseJSON?.message) {
                                    message = xhr.responseJSON.message;
                                }
                                Swal.fire({
                                    title: 'Error!',
                                    text: message,
                                    icon: 'error'
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
@endpush
