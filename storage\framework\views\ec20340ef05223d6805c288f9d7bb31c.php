<?php $__env->startSection('title', 'Tambah Tahun Akademik'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Tambah Tahun Akademik',
        'breadcrumb' => 'Manajemen Akademik',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">
                            <i class="ri-calendar-todo-line text-muted me-1"></i> Tambah Tahun Akademik Baru
                        </h5>
                        <div class="flex-shrink-0">
                            <a href="<?php echo e(route('admin.academic-years.index')); ?>" class="btn btn-soft-danger">
                                <i class="ri-arrow-left-line align-bottom"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div id="error-msg" class="alert alert-danger py-2 d-none"></div>

                    <form id="create-form" method="POST" action="<?php echo e(route('admin.academic-years.store')); ?>">
                        <?php echo csrf_field(); ?>

                        <div class="row g-3">
                            <!-- Basic Information -->
                            <div class="col-lg-6">
                                <div class="card shadow-none border">
                                    <div class="card-header bg-light">
                                        <h5 class="card-title mb-0">Informasi Dasar</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <!-- Name -->
                                            <div class="col-12">
                                                <label for="name" class="form-label">Nama Tahun Akademik <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="name" name="name"
                                                       value="<?php echo e(old('name')); ?>" placeholder="contoh: 2023/2024" required>
                                                <div class="invalid-feedback" data-field="name"></div>
                                            </div>

                                            <!-- Semester -->
                                            <div class="col-12">
                                                <label for="semester" class="form-label">Semester <span class="text-danger">*</span></label>
                                                <select class="form-select" data-choices id="semester" name="semester" required>
                                                    <option value="" selected disabled>Pilih Semester</option>
                                                    <option value="ganjil" <?php echo e(old('semester') == 'ganjil' ? 'selected' : ''); ?>>Ganjil</option>
                                                    <option value="genap" <?php echo e(old('semester') == 'genap' ? 'selected' : ''); ?>>Genap</option>
                                                </select>
                                                <div class="invalid-feedback" data-field="semester"></div>
                                            </div>

                                            <!-- Description -->
                                            <div class="col-12">
                                                <label for="description" class="form-label">Deskripsi (Opsional)</label>
                                                <textarea class="form-control" id="description" name="description"
                                                          rows="3" placeholder="Tambahkan keterangan tambahan jika ada"><?php echo e(old('description')); ?></textarea>
                                                <div class="invalid-feedback" data-field="description"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Date & Status Information -->
                            <div class="col-lg-6">
                                <div class="card shadow-none border">
                                    <div class="card-header bg-light">
                                        <h5 class="card-title mb-0">Periode & Status</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <!-- Start Date -->
                                            <div class="col-12">
                                                <label for="start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                                                <input type="date" class="form-control" id="start_date" name="start_date"
                                                       value="<?php echo e(old('start_date')); ?>" required>
                                                <div class="invalid-feedback" data-field="start_date"></div>
                                            </div>

                                            <!-- End Date -->
                                            <div class="col-12">
                                                <label for="end_date" class="form-label">Tanggal Selesai <span class="text-danger">*</span></label>
                                                <input type="date" class="form-control" id="end_date" name="end_date"
                                                       value="<?php echo e(old('end_date')); ?>" required>
                                                <div class="invalid-feedback" data-field="end_date"></div>
                                            </div>

                                            <!-- Status -->
                                            <div class="col-12">
                                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                                <select class="form-select" data-choices id="status" name="status" required>
                                                    <option value="" selected disabled>Pilih Status</option>
                                                    <option value="planned" <?php echo e(old('status') == 'planned' ? 'selected' : ''); ?>>Direncanakan</option>
                                                    <option value="active" <?php echo e(old('status') == 'active' ? 'selected' : ''); ?>>Aktif</option>
                                                    <option value="completed" <?php echo e(old('status') == 'completed' ? 'selected' : ''); ?>>Selesai</option>
                                                </select>
                                                <small class="text-muted">Pilih status yang sesuai dengan tahun akademik ini.</small>
                                                <div class="invalid-feedback" data-field="status"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="hstack justify-content-end gap-2">
                                    <a href="<?php echo e(route('admin.academic-years.index')); ?>" class="btn btn-ghost-secondary">
                                        <i class="ri-close-line align-bottom"></i> Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <i class="ri-save-line align-bottom"></i> Simpan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <!-- Sweet Alert css-->
    <link href="<?php echo e(asset('assets/libs/sweetalert2/sweetalert2.min.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        $(document).ready(function() {
            // Date validation
            $('#start_date, #end_date').on('change', function() {
                validateDates();
            });

            function validateDates() {
                const startDate = new Date($('#start_date').val());
                const endDate = new Date($('#end_date').val());

                if (startDate && endDate && startDate > endDate) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Tanggal mulai tidak boleh lebih besar dari tanggal selesai!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    $('#end_date').val('');
                }
            }

            // Form submission with AJAX
            $('#create-form').on('submit', function(e) {
                e.preventDefault();

                // Reset previous validation errors
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').text('');
                $('#error-msg').addClass('d-none').text('');

                const submitBtn = $('#submit-btn');
                const originalBtnText = submitBtn.html();

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: $(this).serialize(),
                    beforeSend: function() {
                        submitBtn.prop('disabled', true);
                        submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...');
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            window.location.href = "<?php echo e(route('admin.academic-years.index')); ?>";
                        });
                    },
                    error: function(xhr) {
                        submitBtn.prop('disabled', false);
                        submitBtn.html(originalBtnText);

                        if (xhr.status === 422) {
                            const errors = xhr.responseJSON.errors;

                            // Display validation errors
                            $.each(errors, function(field, messages) {
                                const input = $(`[name="${field}"]`);
                                if (input.length) {
                                    input.addClass('is-invalid');
                                    input.siblings('.invalid-feedback').text(messages[0]);
                                }
                            });

                            // Show general error message
                            $('#error-msg').removeClass('d-none').text('Terdapat kesalahan pada form. Silakan periksa kembali data yang diinputkan.');
                        } else {
                            $('#error-msg').removeClass('d-none').text(xhr.responseJSON?.message || 'Terjadi kesalahan saat menyimpan data.');
                        }
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-mvc\resources\views/admin/pages/academic-year/create.blade.php ENDPATH**/ ?>