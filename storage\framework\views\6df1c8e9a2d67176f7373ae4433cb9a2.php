<div class="app-menu navbar-menu">
    <!-- LOGO -->
    <div class="navbar-brand-box">
        <!-- Dark Logo-->
        <a href="#" class="logo logo-dark">
            <span class="logo-sm">
                <img src="<?php echo e(asset('assets/images/logo-sm.png')); ?>" alt="" height="22">
            </span>
            <span class="logo-lg">
                <img src="<?php echo e(asset('assets/images/logo-dark.png')); ?>" alt="" height="17">
            </span>
        </a>
        <!-- Light Logo-->
        <a href="#" class="logo logo-light">
            <span class="logo-sm">
                <img src="<?php echo e(asset('assets/images/logo-sm.png')); ?>" alt="" height="22">
            </span>
            <span class="logo-lg">
                <img src="<?php echo e(asset('assets/images/logo-light.png')); ?>" alt="" height="17">
            </span>
        </a>
        <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
            <i class="ri-record-circle-line"></i>
        </button>
    </div>

    <div id="scrollbar">
        <div class="container-fluid">
            <div id="two-column-menu"></div>
            <ul class="navbar-nav" id="navbar-nav">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active('admin.dashboard', 'active')); ?>" href="<?php echo e(route('admin.dashboard')); ?>">
                        <i class="mdi mdi-view-dashboard"></i>
                        <span>Dashboard</span>
                    </a>
                </li>

                <!-- PENGGUNA -->
                <li class="menu-title"><i class="ri-user-settings-line"></i> <span data-key="t-users">Pengguna</span></li>

                <!-- Admin & Staff -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.staff.*', 'admin/staff*'], 'active', true)); ?>" href="<?php echo e(route('admin.staff.index')); ?>">
                        <i class="mdi mdi-account-tie"></i>
                        <span>Admin & Staff</span>
                    </a>
                </li>

                <!-- Guru -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.teachers.*', 'admin/teachers*'], 'active', true)); ?>" href="<?php echo e(route('admin.teachers.index')); ?>">
                        <i class="mdi mdi-account-tie"></i>
                        <span>Guru</span>
                    </a>
                </li>

                <!-- Siswa -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.students.*', 'admin/students*'], 'active', true)); ?>" href="<?php echo e(route('admin.students.index')); ?>">
                        <i class="mdi mdi-account-school"></i>
                        <span>Siswa</span>
                    </a>
                </li>

                <!-- AKADEMIK -->
                <li class="menu-title"><i class="ri-book-open-line"></i> <span data-key="t-academic">Akademik</span></li>

                <!-- Tahun Akademik -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.academic-years.*', 'admin/academic-years*'], 'active', true)); ?>" href="<?php echo e(route('admin.academic-years.index')); ?>">
                        <i class="mdi mdi-calendar-range"></i>
                        <span>Tahun Akademik</span>
                    </a>
                </li>

                <!-- Program Studi -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.programs.*', 'admin/programs*'], 'active', true)); ?>" href="<?php echo e(route('admin.programs.index')); ?>">
                        <i class="mdi mdi-certificate"></i>
                        <span>Program Studi</span>
                    </a>
                </li>

                <!-- Mata Pelajaran -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.subjects.*', 'admin/subjects*'], 'active', true)); ?>" href="<?php echo e(route('admin.subjects.index')); ?>">
                        <i class="mdi mdi-book-open-page-variant"></i>
                        <span>Mata Pelajaran</span>
                    </a>
                </li>

                <!-- KELAS -->
                <li class="menu-title"><i class="ri-building-line"></i> <span data-key="t-classroom">Kelas</span></li>

                <!-- Data Kelas -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.classrooms.*', 'admin/classrooms*'], 'active', true)); ?>" href="<?php echo e(route('admin.classrooms.index')); ?>">
                        <i class="mdi mdi-google-classroom"></i>
                        <span>Data Kelas</span>
                    </a>
                </li>

                <!-- Penugasan Guru -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.teacher-assignments.*', 'admin/teacher-assignments*'], 'active', true)); ?>" href="<?php echo e(route('admin.teacher-assignments.index')); ?>">
                        <i class="mdi mdi-account-tie-voice"></i>
                        <span>Penugasan Guru</span>
                    </a>
                </li>

                <!-- JADWAL -->
                <li class="menu-title"><i class="ri-calendar-line"></i> <span data-key="t-schedules">Jadwal</span></li>

                <!-- Shift Belajar -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.shifts.*', 'admin/shifts*'], 'active', true)); ?>" href="<?php echo e(route('admin.shifts.index')); ?>">
                        <i class="mdi mdi-swap-horizontal"></i>
                        <span>Shift Belajar</span>
                    </a>
                </li>

                <!-- Jam Pelajaran -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.lesson-hours.*', 'admin/lesson-hours*'], 'active', true)); ?>" href="<?php echo e(route('admin.lesson-hours.index')); ?>">
                        <i class="mdi mdi-clock-time-four"></i>
                        <span>Jam Pelajaran</span>
                    </a>
                </li>

                <!-- Jadwal Kelas -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.class-schedules.*', 'admin/class-schedules*'], 'active', true)); ?>" href="<?php echo e(route('admin.class-schedules.index')); ?>">
                        <i class="mdi mdi-timetable"></i>
                        <span>Jadwal Kelas</span>
                    </a>
                </li>

                <!-- KEHADIRAN & CUTI -->
                <li class="menu-title"><i class="ri-file-list-line"></i> <span data-key="t-attendance">Absensi & Cuti</span></li>

                <!-- Absensi Siswa -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.attendances.*', 'admin/attendances*'], 'active', true)); ?>" href="<?php echo e(route('admin.attendances.index')); ?>">
                        <i class="mdi mdi-calendar-check"></i>
                        <span>Absensi Siswa</span>
                    </a>
                </li>

                <!-- Absensi Guru -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.attendances.teacher.summary', 'admin/teacher-attendance-summary*'], 'active', true)); ?>" href="<?php echo e(route('admin.attendances.teacher.summary')); ?>">
                        <i class="mdi mdi-clipboard-account"></i>
                        <span>Absensi Guru</span>
                    </a>
                </li>
                <!-- Pengajuan Cuti -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.leave-requests.*', 'admin/leave-requests*'], 'active', true)); ?>" href="<?php echo e(route('admin.leave-requests.index')); ?>">
                        <i class="mdi mdi-calendar-remove"></i>
                        <span>Pengajuan Cuti</span>
                    </a>
                </li>

                <!-- LAPORAN -->
                <li class="menu-title"><i class="ri-file-list-line"></i> <span data-key="t-report">Laporan</span></li>

                <!-- Laporan Absensi Siswa -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.attendances.summary', 'admin/attendances-summary*'], 'active', true)); ?>" href="<?php echo e(route('admin.attendances.summary')); ?>">
                        <i class="mdi mdi-file-chart"></i>
                        <span>Laporan Absensi Siswa</span>
                    </a>
                </li>

                <!-- Laporan Detail Guru -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.attendances.teacher.detailed', 'admin/teacher-attendance-detailed*'], 'active', true)); ?>" href="<?php echo e(route('admin.attendances.teacher.detailed')); ?>">
                        <i class="mdi mdi-file-document-outline"></i>
                        <span>Laporan Detail Guru</span>
                    </a>
                </li>

                <!-- Laporan Global Guru -->
                <li class="nav-item">
                    <a class="nav-link menu-link <?php echo e(is_active(['admin.attendances.teacher.global', 'admin/global-teacher-attendance*'], 'active', true)); ?>" href="<?php echo e(route('admin.attendances.teacher.global')); ?>">
                        <i class="mdi mdi-chart-box"></i>
                        <span>Laporan Global Guru</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\rawooh-mvc\resources\views/admin/partials/sidebar.blade.php ENDPATH**/ ?>