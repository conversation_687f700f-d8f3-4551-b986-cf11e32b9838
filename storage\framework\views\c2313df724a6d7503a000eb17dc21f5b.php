<?php $__env->startSection('title', 'Staf Admin'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Staf Admin',
        'breadcrumb' => 'Manajemen Akun',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar <?php echo $__env->yieldContent('title'); ?>
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-staff">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="export-btn">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-success" id="import-btn">
                                <i class="ri-upload-line align-bottom"></i> Import
                            </button>
                            <a href="<?php echo e(route('admin.staff.create')); ?>" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <!-- Status Dropdown -->
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="filter-status" class="form-label">Status</label>
                                <select class="form-select" data-choices name="status" id="filter-status">
                                    <option value="">Semua Status</option>
                                    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <!-- Role Dropdown -->
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="filter-role" class="form-label">Role</label>
                                <select class="form-select" data-choices name="role" id="filter-role">
                                    <option value="">Semua Role</option>
                                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <!-- Search Input -->
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari nama, email..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>Nama</th>
                                    <th>Email</th>
                                    <th>Nomor Telepon</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="list">
                                <!-- Data akan diisi via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <style type="text/css">
        .dataTables_length,
        .dataTables_filter {
            display: none !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.datatables', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        $(document).ready(function() {
            // Initialize DataTable
            const table = $('#datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "<?php echo e(route('admin.staff.index')); ?>",
                    data: function(d) {
                        d.status = $('#filter-status').val();
                        d.role = $('#filter-role').val();
                        d.search = $('#search-input').val();
                    },
                    complete: function(response) {
                        $('#total-staff').text(response.responseJSON.recordsTotal || 0);
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Error!',
                            text: xhr.responseJSON?.message || 'Gagal memuat data',
                            icon: 'error'
                        });
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'phone_number',
                        name: 'phone_number'
                    },
                    {
                        data: 'role',
                        name: 'role'
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: 'Cari...',
                    lengthMenu: 'Tampilkan _MENU_ data',
                    zeroRecords: 'Data tidak ditemukan',
                    info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                    infoEmpty: 'Menampilkan 0 sampai 0 dari 0 data',
                    infoFiltered: '(disaring dari _MAX_ total data)',
                    paginate: {
                        first: 'Pertama',
                        last: 'Terakhir',
                        next: 'Selanjutnya',
                        previous: 'Sebelumnya'
                    }
                },
                order: [
                    [1, 'asc']
                ] // Sort by name by default
            });

            // Adjust table columns on sidebar toggle
            $('#topnav-hamburger-icon').click(function() {
                setTimeout(() => {
                    table.columns.adjust().draw();
                }, 300);
            });

            // Filter by status
            $('#filter-status').change(function() {
                table.draw();
            });

            // Filter by role
            $('#filter-role').change(function() {
                table.draw();
            });

            // Search on Enter key
            $('#search-input').keyup(function(e) {
                if (e.key === 'Enter') {
                    table.draw();
                }
            });

            // Search button click
            $('#search-button').click(function() {
                table.draw();
            });

            // Export button click
            $('#export-btn').click(function() {
                Swal.fire({
                    title: 'Coming Soon!',
                    text: 'Fitur export akan segera tersedia.',
                    icon: 'info'
                });
            });

            // Import button click
            $('#import-btn').click(function() {
                Swal.fire({
                    title: 'Coming Soon!',
                    text: 'Fitur import akan segera tersedia.',
                    icon: 'info'
                });
            });

            // Handle delete button clicks with event delegation
            $('#datatable').on('click', '.btn-delete-item', function() {
                const url = $(this).data('url');
                if (!url) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'URL hapus tidak ditemukan',
                        icon: 'error'
                    });
                    return;
                }

                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus data staf ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire({
                                        title: 'Berhasil!',
                                        text: response.message,
                                        icon: 'success',
                                        showConfirmButton: false,
                                        timer: 1500
                                    });
                                    table.ajax.reload();
                                } else {
                                    Swal.fire({
                                        title: 'Gagal!',
                                        text: response.message,
                                        icon: 'error'
                                    });
                                }
                            },
                            error: function(xhr) {
                                let message = 'Terjadi kesalahan saat menghapus data';
                                if (xhr.responseJSON?.message) {
                                    message = xhr.responseJSON.message;
                                }
                                Swal.fire({
                                    title: 'Error!',
                                    text: message,
                                    icon: 'error'
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-mvc\resources\views/admin/pages/staff/index.blade.php ENDPATH**/ ?>