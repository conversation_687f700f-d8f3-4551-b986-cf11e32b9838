<?php

namespace App\Http\Requests\StudentRequests;

use App\Enums\GenderEnum;
use App\Enums\UserStatus;
use Illuminate\Foundation\Http\FormRequest;

class StudentFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'gender' => ['sometimes', 'nullable', 'string'],
            'search' => ['sometimes', 'nullable', 'string', 'max:255'],
            'status' => ['sometimes', 'nullable', 'string'],
            'classroom_id' => ['sometimes', 'nullable', 'integer', 'exists:classrooms,id']
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'gender' => 'Jenis kelamin',
            'search' => 'Pencarian',
            'status' => 'Status',
            'classroom_id' => 'Kelas'
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Handle empty string as null for proper filter application
        $this->merge([
            'gender' => $this->gender === '' ? null : $this->gender,
            'status' => $this->status === '' ? null : $this->status,
            'search' => $this->search === '' ? null : $this->search,
            'classroom_id' => $this->classroom_id === '' ? null : $this->classroom_id
        ]);
    }
}
